// Prevent redeclaration errors by checking if variables already exist
if (typeof layoutContainer === 'undefined') {
    var layoutContainer = document.querySelector('.layout-container');
    var defaultLayout = document.querySelector('.layout-default');
    var splitLayout = document.querySelector('.layout-split');
    var tabNavigation = document.querySelector('.tab-navigation');

    var setupContent = document.getElementById('setup');
    var flowContent = document.getElementById('flow');
    var actionsContent = document.getElementById('actions');
}

// --- State and Initialization ---
let originalSetupParent, originalFlowParent, originalActionsParent;

// Function to initialize the layout manager
function initLayoutManager() {
    if (!layoutContainer || !defaultLayout || !splitLayout || !tabNavigation || !setupContent || !flowContent || !actionsContent) {
        // console.warn('Layout manager elements not found, exiting layout manager script.');
        return;
    }

    // Store original parents for resetting views
    originalSetupParent = setupContent.parentNode;
    originalFlowParent = flowContent.parentNode;
    originalActionsParent = actionsContent.parentNode;

    // Set the initial layout
    updateLayout('default');
}

// --- Event Delegation for Layout Switching ---
document.addEventListener('click', function(e) {
    // Find the clicked element or its parent that has the 'data-layout' attribute
    const layoutItem = e.target.closest('[data-layout]');

    if (layoutItem) {
        e.preventDefault();
        const layout = layoutItem.dataset.layout;
        updateLayout(layout);
    }
});

// --- Layout Update Logic ---
function updateLayout(layout) {
    // For 'running' layout, content is cloned, so originals should not be moved.
    if (layout !== 'running') {
        resetContentPositions();
    }

    // Hide all layouts and tab navigation initially
    defaultLayout.classList.add('d-none');
    splitLayout.classList.add('d-none');
    splitLayout.innerHTML = ''; // Clear previous split content
    tabNavigation.classList.add('d-none');

    // Show the selected layout
    switch (layout) {
        case 'default':
            showDefaultLayout();
            break;
        case 'details-flow':
            setupSplitLayout(setupContent, flowContent, 'Details', 'Flow');
            break;
        case 'details-actions':
            setupSplitLayout(setupContent, actionsContent, 'Details', 'Actions');
            break;
        case 'flow-actions':
            setupSplitLayout(flowContent, actionsContent, 'Flow', 'Actions');
            break;
        case 'sync':
            setupSyncView();
            break;
        case 'running':
            setupRunningPage();
            break;
    }
}

// --- Helper Functions for Layout Setup ---

function resetContentPositions() {
    // Ensure original parents exist before attempting to append
    if (originalSetupParent) safeAppend(originalSetupParent, setupContent);
    if (originalFlowParent) safeAppend(originalFlowParent, flowContent);
    if (originalActionsParent) safeAppend(originalActionsParent, actionsContent);

    // Reset classes on content elements
    [setupContent, flowContent, actionsContent].forEach(content => {
        content.classList.remove('w-100', 'h-100', 'overflow-auto', 'p-4', 'col-md-6', 'show', 'active');
    });
}

function setupSplitLayout(leftPanelContent, rightPanelContent, leftHeaderTitle, rightHeaderTitle) {
    splitLayout.classList.remove('d-none');
    splitLayout.innerHTML = `
        <div class="container-fluid h-100 p-0 d-flex flex-column">
            <div class="row g-0 h-100 flex-grow-1">
                <div class="col-md-6 h-100 d-flex flex-column">
                    <div class="p-3 border-bottom bg-light">
                        <h5 class="mb-0">${leftHeaderTitle}</h5>
                    </div>
                    <div class="overflow-auto p-4 flex-grow-1" id="split-left-panel"></div>
                </div>
                <div class="col-md-6 h-100 d-flex flex-column border-start">
                    <div class="p-3 border-bottom bg-light">
                        <h5 class="mb-0">${rightHeaderTitle}</h5>
                    </div>
                    <div class="overflow-auto p-4 flex-grow-1" id="split-right-panel"></div>
                </div>
            </div>
        </div>
    `;
    const leftPanel = document.getElementById('split-left-panel');
    const rightPanel = document.getElementById('split-right-panel');

    if (leftPanel && rightPanel) {
        safeAppend(leftPanel, leftPanelContent);
        safeAppend(rightPanel, rightPanelContent);
        [leftPanelContent, rightPanelContent].forEach(content => {
            content.classList.remove('d-none', 'fade');
            content.classList.add('show', 'active');
        });
    }
}

function setupSyncView() {
    splitLayout.classList.remove('d-none');
    splitLayout.innerHTML = `
        <div class="container-fluid h-100 p-0 d-flex flex-column">
            <div class="row g-0" style="flex: 0 0 25%;">
                <div class="col-12 h-100 d-flex flex-column">
                    <div class="p-3 border-bottom bg-light">
                        <h5 class="mb-0">Details</h5>
                    </div>
                    <div class="overflow-auto p-4 flex-grow-1" id="sync-details-content"></div>
                </div>
            </div>
            <div class="row g-0 flex-grow-1">
                <div class="col-md-6 h-100 d-flex flex-column border-top">
                    <div class="p-3 border-bottom bg-light">
                        <h5 class="mb-0">Flow</h5>
                    </div>
                    <div class="overflow-auto p-4 flex-grow-1" id="sync-flow-content"></div>
                </div>
                <div class="col-md-6 h-100 d-flex flex-column border-top border-start">
                    <div class="p-3 border-bottom bg-light">
                        <h5 class="mb-0">Actions</h5>
                    </div>
                    <div class="overflow-auto p-4 flex-grow-1" id="sync-actions-content"></div>
                </div>
            </div>
        </div>
    `;
    const syncDetailsContainer = document.getElementById('sync-details-content');
    const syncFlowContainer = document.getElementById('sync-flow-content');
    const syncActionsContainer = document.getElementById('sync-actions-content');

    if (syncDetailsContainer && syncFlowContainer && syncActionsContainer) {
        safeAppend(syncDetailsContainer, setupContent);
        safeAppend(syncFlowContainer, flowContent);
        safeAppend(syncActionsContainer, actionsContent);

        [setupContent, flowContent, actionsContent].forEach(content => {
            content.classList.remove('d-none', 'fade');
            content.classList.add('show', 'active');
        });
    }
}

function setupRunningPage() {
    splitLayout.classList.remove('d-none');
    splitLayout.innerHTML = `
        <div class="container-fluid h-100 p-0">
            <div id="running-page-content" class="h-100 overflow-auto p-4"></div>
        </div>
    `;
    const runningPageContainer = document.getElementById('running-page-content');

    if (runningPageContainer) {
        runningPageContainer.innerHTML = ''; // Clear previous content

        const contentToClone = [
            { el: setupContent, title: 'Details' },
            { el: flowContent, title: 'Flow' },
            { el: actionsContent, title: 'Actions' }
        ];

        contentToClone.forEach(item => {
            if (item.el) {
                const clone = item.el.cloneNode(true);
                clone.classList.remove('d-none', 'fade', 'tab-pane');
                clone.classList.add('show', 'active');

                const cardDiv = document.createElement('div');
                cardDiv.classList.add('card', 'mb-4');

                const header = document.createElement('div');
                header.classList.add('card-header', 'bg-light');
                header.innerHTML = `<h5 class="mb-0">${item.title}</h5>`;
                cardDiv.appendChild(header);

                const cardBodyDiv = document.createElement('div');
                cardBodyDiv.classList.add('card-body');

                if (item.el.classList.contains('tab-pane')) {
                    while (clone.firstChild) {
                        cardBodyDiv.appendChild(clone.firstChild);
                    }
                } else {
                    cardBodyDiv.appendChild(clone);
                }
                cardDiv.appendChild(cardBodyDiv);
                runningPageContainer.appendChild(cardDiv);
            }
        });
    }
}

function showDefaultLayout() {
    defaultLayout.classList.remove('d-none');
    tabNavigation.classList.remove('d-none');

    [setupContent, flowContent, actionsContent].forEach(content => {
        if (content) {
            content.classList.remove('show', 'active', 'd-none');
        }
    });

    const activeTabLink = tabNavigation.querySelector('.nav-link.active');
    let activePane = null;

    if (activeTabLink) {
        const activePaneId = activeTabLink.getAttribute('href');
        if (activePaneId) activePane = document.querySelector(activePaneId);
    } else {
        const firstTabLink = tabNavigation.querySelector('.nav-link');
        if (firstTabLink) {
            tabNavigation.querySelectorAll('.nav-link.active').forEach(link => link.classList.remove('active'));
            firstTabLink.classList.add('active');
            const firstPaneId = firstTabLink.getAttribute('href');
            if (firstPaneId) activePane = document.querySelector(firstPaneId);
        }
    }

    if (activePane) {
        activePane.classList.add('show', 'active');
    } else if (setupContent) {
        const firstTabLink = tabNavigation.querySelector('a[href="#setup"]');
        if (firstTabLink) firstTabLink.classList.add('active');
        setupContent.classList.add('show', 'active');
    }
}

function safeAppend(parent, child) {
    if (parent && child && !parent.contains(child)) {
        parent.appendChild(child);
    }
}

// --- Initialize ---
initLayoutManager();