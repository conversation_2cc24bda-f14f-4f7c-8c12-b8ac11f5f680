document.addEventListener('DOMContentLoaded', function() {
    // Handle publishing status dropdown
    const statusOptions = document.querySelectorAll('.status-option');
    const publishingStatusButton = document.getElementById('publishingStatus');

    if (publishingStatusButton) {
        const statusText = publishingStatusButton.querySelector('.status-text');
        const statusIndicator = publishingStatusButton.querySelector('.status-indicator');

        statusOptions.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const newStatus = this.dataset.status;
                const newBg = this.dataset.bg;

                if (statusText) statusText.textContent = newStatus;
                if (statusIndicator) {
                    statusIndicator.className = 'status-indicator me-2'; // Reset classes
                    statusIndicator.classList.add(`bg-${newBg}`);
                }
            });
        });
    }
});
