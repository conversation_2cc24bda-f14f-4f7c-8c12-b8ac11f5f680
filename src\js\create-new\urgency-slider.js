// Urgency Slider functionality for Create New page
console.log('✅ urgency-slider.js loaded');

// Only declare the class if it hasn't been declared already
if (typeof window.UrgencySlider === 'undefined') {
    class UrgencySlider {
        constructor() {
            this.urgencyLevel = 3; // Default to medium (1-5 scale)
            this.urgencyLabels = {
                1: { label: 'Low', color: '#28a745', description: 'Can be scheduled flexibly' },
                2: { label: 'Below Normal', color: '#6f9c3d', description: 'Some flexibility in scheduling' },
                3: { label: 'Normal', color: '#ffc107', description: 'Standard priority meeting' },
                4: { label: 'High', color: '#fd7e14', description: 'Should be scheduled soon' },
                5: { label: 'Urgent', color: '#dc3545', description: 'Needs immediate attention' }
            };
            this.sliderElement = null;
            this.callbacks = [];
            this.init();
        }

        init() {
            console.log('UrgencySlider initialized');
            // Initialize urgency slider functionality
        }

        createSlider(container) {
            if (!container) return;
            
            this.sliderElement = document.createElement('div');
            this.sliderElement.className = 'urgency-slider-container';
            this.sliderElement.innerHTML = this.getSliderHTML();
            
            container.appendChild(this.sliderElement);
            this.attachEvents();
            this.updateDisplay();
            
            console.log('Urgency slider created');
        }

        getSliderHTML() {
            return `
                <div class="urgency-slider">
                    <label class="form-label mb-3">Meeting Urgency Level</label>
                    <div class="slider-wrapper">
                        <input type="range" 
                               class="form-range urgency-range" 
                               id="urgencyRange"
                               min="1" 
                               max="5" 
                               value="${this.urgencyLevel}"
                               step="1">
                        <div class="urgency-markers">
                            ${this.getMarkersHTML()}
                        </div>
                    </div>
                    <div class="urgency-display mt-3">
                        <div class="urgency-indicator">
                            <span class="urgency-dot"></span>
                            <span class="urgency-label"></span>
                        </div>
                        <div class="urgency-description text-muted small"></div>
                    </div>
                    <div class="urgency-effects mt-3">
                        <div class="alert alert-info urgency-info">
                            <i class="ti-info-circle me-2"></i>
                            <span class="urgency-effect-text"></span>
                        </div>
                    </div>
                </div>
            `;
        }

        getMarkersHTML() {
            return Object.keys(this.urgencyLabels).map(level => `
                <div class="marker" data-level="${level}">
                    <div class="marker-dot" style="background-color: ${this.urgencyLabels[level].color}"></div>
                    <div class="marker-label">${this.urgencyLabels[level].label}</div>
                </div>
            `).join('');
        }

        attachEvents() {
            const rangeInput = this.sliderElement.querySelector('#urgencyRange');
            if (rangeInput) {
                rangeInput.addEventListener('input', (e) => {
                    this.setUrgencyLevel(parseInt(e.target.value));
                });
                
                rangeInput.addEventListener('change', (e) => {
                    this.triggerCallbacks(parseInt(e.target.value));
                });
            }
            
            // Add click handlers for markers
            const markers = this.sliderElement.querySelectorAll('.marker');
            markers.forEach(marker => {
                marker.addEventListener('click', () => {
                    const level = parseInt(marker.getAttribute('data-level'));
                    this.setUrgencyLevel(level);
                    this.triggerCallbacks(level);
                });
            });
        }

        setUrgencyLevel(level) {
            if (level < 1 || level > 5) return;
            
            this.urgencyLevel = level;
            this.updateDisplay();
            console.log('Urgency level set to:', level, this.urgencyLabels[level].label);
        }

        updateDisplay() {
            if (!this.sliderElement) return;
            
            const currentUrgency = this.urgencyLabels[this.urgencyLevel];
            
            // Update range input
            const rangeInput = this.sliderElement.querySelector('#urgencyRange');
            if (rangeInput) {
                rangeInput.value = this.urgencyLevel;
            }
            
            // Update indicator
            const dot = this.sliderElement.querySelector('.urgency-dot');
            const label = this.sliderElement.querySelector('.urgency-label');
            const description = this.sliderElement.querySelector('.urgency-description');
            
            if (dot) {
                dot.style.backgroundColor = currentUrgency.color;
            }
            
            if (label) {
                label.textContent = currentUrgency.label;
                label.style.color = currentUrgency.color;
            }
            
            if (description) {
                description.textContent = currentUrgency.description;
            }
            
            // Update effect text
            this.updateEffectText();
            
            // Update markers
            this.updateMarkers();
        }

        updateEffectText() {
            const effectText = this.sliderElement.querySelector('.urgency-effect-text');
            if (!effectText) return;
            
            const effects = {
                1: 'This meeting can be scheduled at your convenience with flexible timing.',
                2: 'Participants will receive standard notifications with some scheduling flexibility.',
                3: 'Standard meeting priority with normal notification timing and scheduling.',
                4: 'Participants will receive priority notifications and scheduling preferences.',
                5: 'This meeting will be marked as urgent with immediate notifications and priority scheduling.'
            };
            
            effectText.textContent = effects[this.urgencyLevel];
            
            // Update alert class based on urgency
            const alertElement = this.sliderElement.querySelector('.urgency-info');
            if (alertElement) {
                alertElement.className = 'alert urgency-info';
                
                if (this.urgencyLevel <= 2) {
                    alertElement.classList.add('alert-success');
                } else if (this.urgencyLevel === 3) {
                    alertElement.classList.add('alert-info');
                } else if (this.urgencyLevel === 4) {
                    alertElement.classList.add('alert-warning');
                } else {
                    alertElement.classList.add('alert-danger');
                }
            }
        }

        updateMarkers() {
            const markers = this.sliderElement.querySelectorAll('.marker');
            markers.forEach(marker => {
                const level = parseInt(marker.getAttribute('data-level'));
                const isActive = level === this.urgencyLevel;
                
                marker.classList.toggle('active', isActive);
                
                if (isActive) {
                    marker.style.transform = 'scale(1.1)';
                } else {
                    marker.style.transform = 'scale(1)';
                }
            });
        }

        getUrgencyLevel() {
            return this.urgencyLevel;
        }

        getUrgencyData() {
            return {
                level: this.urgencyLevel,
                ...this.urgencyLabels[this.urgencyLevel]
            };
        }

        onUrgencyChange(callback) {
            if (typeof callback === 'function') {
                this.callbacks.push(callback);
            }
        }

        triggerCallbacks(level) {
            this.callbacks.forEach(callback => {
                try {
                    callback(level, this.getUrgencyData());
                } catch (error) {
                    console.error('Error in urgency callback:', error);
                }
            });
        }

        removeCallback(callback) {
            this.callbacks = this.callbacks.filter(cb => cb !== callback);
        }

        reset() {
            this.setUrgencyLevel(3); // Reset to normal
            console.log('Urgency slider reset to normal');
        }

        destroy() {
            if (this.sliderElement && this.sliderElement.parentNode) {
                this.sliderElement.parentNode.removeChild(this.sliderElement);
                this.sliderElement = null;
            }
            this.callbacks = [];
            console.log('Urgency slider destroyed');
        }
    }

    // Export for global use
    window.UrgencySlider = UrgencySlider;
}